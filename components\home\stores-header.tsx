"use client";

import { SearchIcon, X } from "lucide-react";
import { useState } from "react";
import { Input } from "../ui/input";

export default function StoresHeader() {
  const [searchValue, setSearchValue] = useState("");

  return (
    <div className="flex flex-col justify-center gap-5 lg:flex-row lg:items-center lg:gap-20">
      <div>
        <h2 className="text-[24px] font-[600] lg:text-[48px]">الخصومات</h2>
        <p className="text-[14px] font-[400] text-[#585A5D] lg:text-[20px]">
          ابحث عن أفضل العروض والخصومات بسهولة!
        </p>
      </div>

      <div className="relative h-[50px] lg:h-[80px] lg:flex-1">
        <Input
          type="text"
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          className="h-full rounded-[100px] border-0 bg-[#F6F6F6] pr-14 pl-5 !text-[12px] lg:!text-[20px]"
          placeholder="ابحث عن المتاجر ..."
        />

        <SearchIcon
          color="#6F6F6F"
          className="absolute top-[50%] right-5 size-[20px] -translate-y-1/2 lg:size-[24px]"
        />

        {searchValue && (
          <button
            onClick={() => setSearchValue("")}
            className="absolute top-[50%] left-5 flex h-[16px] w-[16px] -translate-y-1/2 cursor-pointer items-center justify-center rounded-full bg-[#818187] p-0.5 text-white transition-colors lg:h-[28px] lg:w-[28px]"
          >
            <X className="size-4 lg:size-5" />
          </button>
        )}
      </div>
    </div>
  );
}
