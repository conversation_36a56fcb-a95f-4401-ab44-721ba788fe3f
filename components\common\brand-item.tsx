import Image from "next/image";
import Link from "next/link";
import { Button } from "../ui/button";
import { Card } from "../ui/card";

type BrandItemProps = {
  id: number;
  image: string;
  title: string;
};

export default function BrandItem({ id, image, title }: BrandItemProps) {
  return (
    <Card className="flex flex-col items-center gap-3 rounded-[30px] p-6">
      <Image src={image} alt={title} priority />
      <span className="-mt-5 text-[20px] font-[600]">{title}</span>
      <Link href={`/coupons/${id}`}>
        <Button className="cursor-pointer rounded-full px-10">
          عطني الكود
        </Button>
      </Link>
    </Card>
  );
}
