"use client";

import { openAddStoreModal } from "@/atoms/open-atoms";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { ScrollArea } from "../ui/scroll-area";

const FormSchema = z.object({
  name: z.string().nonempty({ message: "يرجي ادخال الاسم" }),
  email: z
    .string()
    .nonempty({
      message: "البريد الالكتروني مطلوب",
    })
    .email({ message: "البريد الالكتروني غي صحيح" }),
  phoneNumber: z.string().nonempty({ message: "رقم الجوال مطلوب" }),
  storeName: z.string().nonempty({ message: "اسم المتجر مطلوب" }),
  storeUrl: z.string().nonempty({ message: "رابط المتجر مطلوب" }),
});

export default function AddStoreDialog() {
  const isOpened = openAddStoreModal.useOpened();

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: "",
      email: "",
      phoneNumber: "",
      storeName: "",
      storeUrl: "",
    },
  });
  function onSubmit(data: z.infer<typeof FormSchema>) {
    console.log(data);
  }

  return (
    <Dialog open={isOpened} onOpenChange={openAddStoreModal.toggle}>
      <DialogContent className="rounded-3xl">
        <ScrollArea dir="rtl" className="h-[500px] px-14 py-7">
          <DialogHeader>
            <DialogTitle className="text-center text-[24px] font-[700] lg:text-[32px]">
              أضف متجرك
            </DialogTitle>
            <div className="my-5 border" />
            <DialogDescription></DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="flex flex-col gap-5"
            >
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>الاسم</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="ادخل اسمك الكامل"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>البريد الإلكتروني</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="ادخل بريدك الإلكتروني"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="phoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>رقم الجوال</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="ادخل رقم الجوال"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="storeName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>اسم المتجر</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="ادخل اسم المتجر"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="storeUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>رابط المتجر</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="ادخل رابط المتجر"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="my-5 border" />

              <Button
                type="submit"
                className="h-[48px] cursor-pointer rounded-full font-[700]"
              >
                إرسال
              </Button>
            </form>
          </Form>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
