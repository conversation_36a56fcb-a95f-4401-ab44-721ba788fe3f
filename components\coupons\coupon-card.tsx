"use client";

import { Calendar, Copy, Tag } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

import { Button } from "../ui/button";
import { Card } from "../ui/card";
import { Tooltip } from "../ui/tooltip";

interface CouponCardProps {
  id: number;
  title: string;
  description: string;
  discount: string;
  discountType: "percentage" | "fixed";
  store: string;
  storeImage: string;
  category: string;
  code: string;
  expiryDate: string;
  terms: string;
  isActive: boolean;
  minPurchase: number;
}

export default function CouponCard({
  title,
  description,
  discount,
  discountType,
  store,
  storeImage,
  code,
  expiryDate,
  terms,
  isActive,
  minPurchase,
}: CouponCardProps) {
  const [copied, setCopied] = useState(false);

  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy code:", err);
    }
  };

  const formatExpiryDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("ar-SA", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const isExpired = new Date(expiryDate) < new Date();

  return (
    <Card className={`overflow-hidden transition-all hover:shadow-lg ${isExpired ? "opacity-60" : ""}`}>
      <div className="relative">
        {/* Discount Badge */}
        <div className="absolute left-4 top-4 z-10 rounded-full bg-red-500 px-3 py-1 text-sm font-bold text-white">
          {discountType === "percentage" ? discount : `${discount} ريال`}
        </div>
        
        {/* Store Logo */}
        <div className="flex h-32 items-center justify-center bg-gray-50 p-4">
          <Image
            src={storeImage}
            alt={store}
            className="h-16 w-16 object-contain"
            width={64}
            height={64}
          />
        </div>
      </div>

      <div className="p-4">
        {/* Store Name */}
        <div className="mb-2 text-sm font-medium text-gray-600">{store}</div>

        {/* Title */}
        <h3 className="mb-2 text-lg font-bold text-gray-900 line-clamp-2">
          {title}
        </h3>

        {/* Description */}
        <p className="mb-4 text-sm text-gray-600 line-clamp-2">{description}</p>

        {/* Coupon Code */}
        <div className="mb-4 rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Tag className="h-4 w-4 text-gray-500" />
              <span className="font-mono text-lg font-bold text-gray-900">
                {code}
              </span>
            </div>
            <Tooltip content={copied ? "تم النسخ!" : "انسخ الكود"}>
              <Button
                size="sm"
                variant="outline"
                onClick={handleCopyCode}
                className="h-8 w-8 p-0"
              >
                <Copy className="h-3 w-3" />
              </Button>
            </Tooltip>
          </div>
        </div>

        {/* Details */}
        <div className="mb-4 space-y-2 text-xs text-gray-500">
          <div className="flex items-center gap-2">
            <Calendar className="h-3 w-3" />
            <span>ينتهي في: {formatExpiryDate(expiryDate)}</span>
          </div>
          <div>حد أدنى للشراء: {minPurchase} ريال</div>
        </div>

        {/* Terms */}
        <div className="mb-4 rounded-md bg-blue-50 p-2 text-xs text-blue-800">
          <strong>الشروط والأحكام:</strong> {terms}
        </div>

        {/* Action Button */}
        <Button
          className="w-full"
          disabled={isExpired || !isActive}
          onClick={handleCopyCode}
        >
          {isExpired ? "انتهت صلاحية الكوبون" : copied ? "تم النسخ!" : "احصل على الكوبون"}
        </Button>
      </div>
    </Card>
  );
}
