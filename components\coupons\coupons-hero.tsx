import Image from "next/image";

import percentage from "@/assets/percentage.svg";

export default function CouponsHero() {
  return (
    <section className="bg-gradient-to-r from-blue-50 to-purple-50 py-16">
      <div className="container">
        <div className="flex flex-col items-center justify-between gap-8 lg:flex-row">
          <div className="flex-1 text-center lg:text-right">
            <h1 className="mb-4 text-4xl font-bold text-gray-900 lg:text-5xl">
              الكوبونات والخصومات
            </h1>
            <p className="mb-6 text-lg text-gray-600 lg:text-xl">
              اكتشف أفضل العروض والخصومات الحصرية من متاجرك المفضلة
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center lg:justify-start">
              <div className="rounded-lg bg-white p-4 shadow-md">
                <div className="text-2xl font-bold text-blue-600">500+</div>
                <div className="text-sm text-gray-600">كوبون متاح</div>
              </div>
              <div className="rounded-lg bg-white p-4 shadow-md">
                <div className="text-2xl font-bold text-green-600">70%</div>
                <div className="text-sm text-gray-600">خصم يصل إلى</div>
              </div>
              <div className="rounded-lg bg-white p-4 shadow-md">
                <div className="text-2xl font-bold text-purple-600">100+</div>
                <div className="text-sm text-gray-600">متجر شريك</div>
              </div>
            </div>
          </div>
          <div className="flex-1 flex justify-center">
            <Image
              src={percentage}
              alt="خصومات وكوبونات"
              className="h-64 w-64 object-contain lg:h-80 lg:w-80"
              priority
            />
          </div>
        </div>
      </div>
    </section>
  );
}
