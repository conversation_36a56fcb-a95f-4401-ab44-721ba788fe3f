"use client";

import { Search } from "lucide-react";
import { useState } from "react";

import { couponCategories } from "@/data";

import { Button } from "../ui/button";
import { Input } from "../ui/input";

interface CouponsFiltersProps {
  onCategoryChange?: (category: string) => void;
  onSearchChange?: (search: string) => void;
}

export default function CouponsFilters({
  onCategoryChange,
  onSearchChange,
}: CouponsFiltersProps) {
  const [activeCategory, setActiveCategory] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");

  const handleCategoryChange = (categoryId: string) => {
    setActiveCategory(categoryId);
    onCategoryChange?.(categoryId);
  };

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    onSearchChange?.(value);
  };

  return (
    <div className="mb-8 space-y-6">
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
        <Input
          type="text"
          placeholder="ابحث عن كوبون أو متجر..."
          value={searchTerm}
          onChange={(e) => handleSearchChange(e.target.value)}
          className="pr-10 text-right"
        />
      </div>

      {/* Category Filters */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">التصنيفات</h3>
        <div className="flex flex-wrap gap-2">
          {couponCategories.map((category) => (
            <Button
              key={category.id}
              variant={activeCategory === category.id ? "default" : "outline"}
              size="sm"
              onClick={() => handleCategoryChange(category.id)}
              className="rounded-full"
            >
              {category.name}
            </Button>
          ))}
        </div>
      </div>

      {/* Sort Options */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">ترتيب حسب:</span>
          <select className="rounded-md border border-gray-300 px-3 py-1 text-sm">
            <option value="newest">الأحدث</option>
            <option value="discount">أعلى خصم</option>
            <option value="expiry">تاريخ الانتهاء</option>
            <option value="popular">الأكثر شعبية</option>
          </select>
        </div>
        <div className="text-sm text-gray-600">
          عرض جميع الكوبونات المتاحة
        </div>
      </div>
    </div>
  );
}
