import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";
import Image, { StaticImageData } from "next/image";

interface FeatureSectionProps {
  title: string;
  subtitle: string;
  description: string;
  features: string[];
  image: StaticImageData;
  imageBgColor: string;
  badgeBgColor: string;
  badgeText: string;
  reverse?: boolean;
}

export function FeatureSection({
  title,
  subtitle,
  description,
  features,
  image,
  imageBgColor,
  badgeBgColor,
  badgeText,
  reverse = false,
}: FeatureSectionProps) {
  const content = (
    <div className="flex flex-1 flex-col gap-5 lg:mt-10">
      <div
        className={`w-fit rounded-full ${badgeBgColor} px-4 py-1 text-[18px] font-[700] lg:text-[20px]`}
      >
        {badgeText}
      </div>

      <h2 className="text-[24px] font-[600] lg:text-[48px]">{title}</h2>

      <div className="text-[16px] font-[400] lg:text-[20px]">{description}</div>

      <div className="flex flex-col gap-2 text-[16px] font-[400] lg:text-[20px]">
        {features.map((feature, index) => (
          <div key={index} className="flex items-center gap-2">
            <Check size={14} />
            {feature}
          </div>
        ))}
      </div>

      <Button className="mt-10 h-[48px] w-fit cursor-pointer rounded-full px-7">
        انضم الحين
      </Button>
    </div>
  );

  const imageSection = (
    <div className={`rounded-[48px] ${imageBgColor} flex-1 pb-10`}>
      <Image src={image} alt={subtitle} className="m-auto" />
    </div>
  );

  if (reverse) {
    return (
      <div className="flex flex-col-reverse gap-10 lg:flex-row">
        {content}
        {imageSection}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-10 lg:grid-cols-2">
      {imageSection}
      {content}
    </div>
  );
}
