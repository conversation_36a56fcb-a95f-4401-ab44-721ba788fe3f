import logo from "@/assets/light_logo.svg";
import Image from "next/image";
import Link from "next/link";

import app_store_image from "@/assets/app_syore.svg";
import google_play_image from "@/assets/google_play.svg";
import qr_code_image from "@/assets/qr_code.svg";

import linked_image from "@/assets/linkedin.svg";
import twitter_image from "@/assets/twitter.svg";

import instagram_image from "@/assets/instgram.svg";
import ticktok_image from "@/assets/ticktok.svg";

export default function Footer() {
  return (
    <div className="flex flex-col gap-20 bg-black py-20">
      <div className="gird-cols-1 container grid gap-10 lg:grid-cols-2">
        <div className="flex w-[300px] max-w-full flex-col gap-16">
          <div className="flex flex-col gap-10">
            <Image src={logo} alt="logo" />
            <p className="leading-[24px] font-[400] text-white">
              خصوماتك يساعدك على التوفير الذكي، مع أحدث أكواد الخصم والعروض
              الحصرية من أشهر المتاجر.
            </p>
          </div>

          <div className="grid grid-cols-3 gap-3 font-[500] text-white">
            <Link href={"/"} className="text-[20px] leading-[30px]">
              خصوماتك
            </Link>
            <Link href={"/"} className="text-[20px] leading-[30px]">
              الأعمال
            </Link>
            <Link href={"/"} className="text-[20px] leading-[30px]">
              الدعم الفني
            </Link>
            <Link
              href={"/blog"}
              className="text-[14px] leading-[30px] text-[#FFFFFFB2]"
            >
              مدونة خصوماتك
            </Link>
            <Link
              href={"/"}
              className="text-[14px] leading-[30px] text-[#FFFFFFB2]"
            >
              كن شريكاً
            </Link>
            <Link
              href={"/"}
              className="text-[14px] leading-[30px] text-[#FFFFFFB2]"
            >
              تواصل معنا
            </Link>
          </div>
        </div>

        <div className="flex flex-col gap-16">
          <div className="flex flex-col gap-10">
            <h3 className="text-[20px] font-[500] text-white">حمل التطبيق</h3>
            <div className="flex flex-row items-center gap-5">
              <Link href={"/"}>
                <Image src={app_store_image} alt="app store" />
              </Link>
              <Link href={"/"}>
                <Image src={google_play_image} alt="google play" />
              </Link>
            </div>
          </div>

          {/*  */}

          <div className="flex items-center gap-5">
            <Image src={qr_code_image} alt="qr code" />
            <p className="w-[216px] max-w-full font-[500] text-[#FFFFFFB2]">
              امسح الباركود وحمل تطبيق خصوماتك
            </p>
          </div>
        </div>
      </div>

      {/*  */}

      <div className="gird-cols-1 container grid gap-10 text-[#FFFFFFB2] lg:grid-cols-2">
        <div className="flex gap-5">
          <Link href={"/"} className="hover:underline">
            حقوق الملكية
          </Link>
          <Link href={"/privacy-policy"} className="hover:underline">
            سياسة الخصوصية
          </Link>
        </div>

        <div className="flex items-center gap-5">
          <span className="text-[20px] font-[600]">تابعنا</span>
          <div className="flex items-center gap-5">
            <Link href={"/"}>
              <Image src={twitter_image} alt="twitter" />
            </Link>
            <Link href={"/"}>
              <Image src={linked_image} alt="linkedin" />
            </Link>
            <Link href={"/"}>
              <Image src={instagram_image} alt="instagram" />
            </Link>
            <Link href={"/"}>
              <Image src={ticktok_image} alt="ticktok" />
            </Link>
          </div>
        </div>
      </div>

      {/*  */}

      <p className="container border-t border-[#272727] pt-5 text-left text-[#FFFFFFB2]">
        ، المملكة العربية السعودية. © 2025
      </p>
    </div>
  );
}
