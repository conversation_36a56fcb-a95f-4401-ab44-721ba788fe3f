import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface FAQItem {
  id: string;
  question: string;
  answer: string;
}

const items: FAQItem[] = [
  {
    id: "item-1",
    question: "كيف يمكنني إضافة متجري إلى التطبيق؟",
    answer:
      "يمكنك إضافة متجرك بسهولة من خلال اتباع الخطوات التالية: 1) قم بإنشاء حساب جديد 2) املأ معلومات المتجر الأساسية 3) أضف منتجاتك 4) قم بتفعيل حسابك. فريقنا سيراجع طلبك خلال 24 ساعة.",
  },
  {
    id: "item-2",
    question: "هل الانضمام إلى التطبيق مجاني؟",
    answer:
      "نعم، الانضمام إلى التطبيق مجاني تماماً. نحن نقدم خطة أساسية مجانية تتيح لك إضافة متجرك وعرض منتجاتك. كما نوفر خطط مدفوعة إضافية مع مميزات متقدمة مثل إحصائيات متقدمة وإدارة متعددة للمتاجر.",
  },
  {
    id: "item-3",
    question: "كيف أُنشئ كود خصم خاص بمتجري؟",
    answer:
      "يمكنك إنشاء كود خصم خاص بمتجرك من خلال لوحة التحكم الخاصة بك. اختر 'إدارة الأكواد' ثم 'إنشاء كود جديد'. يمكنك تحديد نسبة الخصم، تاريخ الصلاحية، وعدد مرات الاستخدام المسموح بها.",
  },
  {
    id: "item-4",
    question: "هل يمكن تخصيص الأكواد لفئات محددة من المنتجات؟",
    answer:
      "نعم، يمكنك تخصيص أكواد الخصم لفئات محددة من المنتجات. عند إنشاء الكود، يمكنك تحديد الفئات التي ينطبق عليها الخصم، أو استثناء فئات معينة. كما يمكنك تحديد حد أدنى للشراء لتفعيل الخصم.",
  },
];

export default function FAQ() {
  return (
    <div>
      <h2 className="text-center text-[24px] font-[600] lg:text-[40px]">
        الأسئلة الشائعة
      </h2>

      <Accordion
        type="single"
        collapsible
        className="m-auto w-full lg:w-[70%]"
        defaultValue={items[0]?.id}
      >
        {items.map((item) => (
          <AccordionItem key={item.id} value={item.id}>
            <AccordionTrigger className="pb-10 text-[16px] font-[700] lg:text-[24px]">
              {item.question}
            </AccordionTrigger>
            <AccordionContent className="flex flex-col gap-4 text-balance">
              {item.answer}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
}
