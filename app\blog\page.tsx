import BackButton from "@/components/common/back-button";
import { blogs } from "@/data";
import { BlogCard } from "../../components/blog/BlogCard";
import { BlogTabs } from "../../components/blog/BlogTabs";

export const metadata = {
  title: "مدونة خصوماتك",
  description:
    "اكتشف أحدث الأخبار، قصص النجاح، التحليلات، ومراجعات المتاجر في مدونة خصوماتك.",
};

export default function BlogPage() {
  // bg-gradient-to-l from-[#FFD8D8] to-[#FFFFFF]
  return (
    <div className="py-20">
      <div className="container flex flex-col gap-10">
        <h1 className="text-center text-[32px] font-[700] lg:text-[64px]">
          مدونة خصوماتك
        </h1>

        <BackButton href={"/"} label="العودة" />

        <BlogTabs />

        <div className="grid grid-cols-1 gap-10 md:grid-cols-2 lg:grid-cols-3">
          {blogs.map((blog) => (
            <BlogCard key={blog.id} {...blog} />
          ))}
        </div>
      </div>
    </div>
  );
}
