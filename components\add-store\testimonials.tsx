import { Marquee } from "@/components/magicui/marquee";
import { cn } from "@/lib/utils";
import Image from "next/image";

const reviews = [
  {
    name: "محمد",
    username: "@jack",
    body: "المنصة ساعدتنا نكسب عملاء جدد ما كنا نقدر نوصلهم بدونهم. شكراً لفريق خصوماتك على هذا التعاون الراقي.",
    img: "https://avatar.vercel.sh/jack",
  },
  {
    name: "محمد",
    username: "@jill",
    body: "المنصة ساعدتنا نكسب عملاء جدد ما كنا نقدر نوصلهم بدونهم. شكراً لفريق خصوماتك على هذا التعاون الراقي.",
    img: "https://avatar.vercel.sh/jill",
  },
  {
    name: "محم<PERSON>",
    username: "@john",
    body: "المنصة ساعدتنا نكسب عملاء جدد ما كنا نقدر نوصلهم بدونهم. شكراً لفريق خصوماتك على هذا التعاون الراقي.",
    img: "https://avatar.vercel.sh/john",
  },
  {
    name: "محمد",
    username: "@jane",
    body: "المنصة ساعدتنا نكسب عملاء جدد ما كنا نقدر نوصلهم بدونهم. شكراً لفريق خصوماتك على هذا التعاون الراقي.",
    img: "https://avatar.vercel.sh/jane",
  },
  {
    name: "محمد",
    username: "@jenny",
    body: "المنصة ساعدتنا نكسب عملاء جدد ما كنا نقدر نوصلهم بدونهم. شكراً لفريق خصوماتك على هذا التعاون الراقي.",
    img: "https://avatar.vercel.sh/jenny",
  },
  {
    name: "محمد",
    username: "@james",
    body: "المنصة ساعدتنا نكسب عملاء جدد ما كنا نقدر نوصلهم بدونهم. شكراً لفريق خصوماتك على هذا التعاون الراقي.",
    img: "https://avatar.vercel.sh/james",
  },
];

const firstRow = reviews.slice(0, reviews.length / 2);
const secondRow = reviews.slice(reviews.length / 2);

const ReviewCard = ({
  img,
  name,
  username,
  body,
}: {
  img: string;
  name: string;
  username: string;
  body: string;
}) => {
  return (
    <figure
      className={cn(
        "relative h-full w-[440px] cursor-pointer overflow-hidden rounded-xl border bg-white p-4",
        // // light styles
        // "border-gray-950/[.1] bg-gray-950/[.01] hover:bg-gray-950/[.05]",
        // // dark styles
        // "dark:border-gray-50/[.1] dark:bg-gray-50/[.10] dark:hover:bg-gray-50/[.15]",
      )}
    >
      <div className="flex flex-row items-center gap-2">
        <Image
          className="rounded-full"
          width="40"
          height="40"
          alt=""
          src={img}
        />
        <div className="flex flex-col">
          <figcaption className="text-sm font-medium">{name}</figcaption>
          <p className="text-xs font-medium">{username}</p>
        </div>
      </div>
      <blockquote className="mt-2 text-sm">{body}</blockquote>
    </figure>
  );
};

export function Testimonials() {
  return (
    <div>
      <h2 className="mb-10 text-center text-[24px] font-[600] lg:text-[48px]">
        آراء المستخدمين عن خصوماتك
      </h2>
      <div className="relative flex w-full flex-col items-center justify-center overflow-hidden">
        <Marquee pauseOnHover className="[--duration:20s]">
          {firstRow.map((review) => (
            <ReviewCard key={review.username} {...review} />
          ))}
        </Marquee>
        <Marquee reverse pauseOnHover className="[--duration:20s]">
          {secondRow.map((review) => (
            <ReviewCard key={review.username} {...review} />
          ))}
        </Marquee>
        <div className="from-background pointer-events-none absolute inset-y-0 left-0 w-1/4 bg-gradient-to-r"></div>
        <div className="from-background pointer-events-none absolute inset-y-0 right-0 w-1/4 bg-gradient-to-l"></div>
      </div>
    </div>
  );
}
