"use client";

import { useState } from "react";

import { coupons } from "@/data";

import CouponCard from "./coupon-card";
import CouponsFilters from "./coupons-filters";

export default function CouponsGrid() {
  const [filteredCoupons, setFilteredCoupons] = useState(coupons);
  const [activeCategory, setActiveCategory] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");

  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    filterCoupons(category, searchTerm);
  };

  const handleSearchChange = (search: string) => {
    setSearchTerm(search);
    filterCoupons(activeCategory, search);
  };

  const filterCoupons = (category: string, search: string) => {
    let filtered = coupons;

    // Filter by category
    if (category !== "all") {
      filtered = filtered.filter((coupon) => coupon.category === category);
    }

    // Filter by search term
    if (search.trim()) {
      filtered = filtered.filter(
        (coupon) =>
          coupon.title.toLowerCase().includes(search.toLowerCase()) ||
          coupon.store.toLowerCase().includes(search.toLowerCase()) ||
          coupon.description.toLowerCase().includes(search.toLowerCase())
      );
    }

    setFilteredCoupons(filtered);
  };

  return (
    <div>
      <CouponsFilters
        onCategoryChange={handleCategoryChange}
        onSearchChange={handleSearchChange}
      />
      
      {filteredCoupons.length === 0 ? (
        <div className="py-16 text-center">
          <div className="mx-auto mb-4 h-24 w-24 rounded-full bg-gray-100 flex items-center justify-center">
            <svg
              className="h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
          </div>
          <h3 className="mb-2 text-lg font-medium text-gray-900">
            لا توجد كوبونات متاحة
          </h3>
          <p className="text-gray-500">
            جرب تغيير المرشحات أو البحث عن شيء آخر
          </p>
        </div>
      ) : (
        <>
          <div className="mb-6 flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-900">
              الكوبونات المتاحة ({filteredCoupons.length})
            </h2>
          </div>
          
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {filteredCoupons.map((coupon) => (
              <CouponCard key={coupon.id} {...coupon} />
            ))}
          </div>
        </>
      )}
    </div>
  );
}
