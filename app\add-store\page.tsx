import attension_image from "@/assets/attension.svg";
import distinguish_image from "@/assets/distinguish.svg";
import grow_image from "@/assets/grow.svg";

import { FeatureSection } from "@/components/add-store/FeatureSection";
import { HeroSection } from "@/components/add-store/HeroSection";

export const metadata = {
  title: "أضف متجرك",
  description: "أضف متجرك إلى خصوماتك",
};

export default function AddStorePage() {
  return (
    <div>
      <div className="container flex flex-col gap-20 py-20">
        <HeroSection />

        <FeatureSection
          title="تميّز بين المتاجر"
          subtitle="distinguish between stores"
          description="كن جزءًا من واجهة التطبيق، وامنح علامتك مساحة بارزة يراها المستخدمون باستمرار."
          features={[
            "تواجد في تطبيق خصوماتك بشكل مميز",
            "استفد من الثقة التي بنتها المنصة مع المستخدمين",
            "وسّع قاعدة عملائك عبر الظهور في واجهة العروض",
          ]}
          image={distinguish_image}
          imageBgColor="bg-[#FF9191]"
          badgeBgColor="bg-[#FFE9E9]"
          badgeText="عزّز ظهور علامتك التجارية"
        />

        <FeatureSection
          title="اجذب اهتمام المتسوقين"
          subtitle="attension between stores"
          description="قدّم عروضًا مميزة بكود خصم حصري، وابدأ بجذب المهتمين بالتسوّق من أول زيارة."
          features={[
            "تواصل مع المزيد من الزبائن المهتمين بعروضك",
            "قدم أكواد خصم حصرية تشجع على الشراء",
            "اربطهم بتجربتك بسهولة من خلال تطبيق خصوماتك",
          ]}
          image={attension_image}
          imageBgColor="bg-[#F9AFAD]"
          badgeBgColor="bg-[#FFF6D7]"
          badgeText="اجذب المزيد من العملاء"
          reverse
        />

        <FeatureSection
          title="حقق نموًا حقيقيًا"
          subtitle="distinguish between stores"
          description="استفد من أدوات التحليل الذكية والعروض القابلة للتتبع لزيادة الطلبات وتحسين الأداء."
          features={[
            "زيادة في الطلبات من خلال كود الخصم",
            "وصول أسرع لجمهور واسع مستهدف",
            "دعم تحليلي لأداء العروض بشكل مستمر",
          ]}
          image={grow_image}
          imageBgColor="bg-[#FFE9E9]"
          badgeBgColor="bg-[#E8EAFF]"
          badgeText="زد مبيعاتك"
        />
      </div>
    </div>
  );
}
